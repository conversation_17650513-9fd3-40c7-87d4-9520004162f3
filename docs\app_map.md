# Mappa dell'Applicazione ASDP
Ultimo aggiornamento: 2025-06-12

### Struttura File e Cartelle

```markdown
- `Ilmazza/asdp/` (radice del progetto `c:\xampp\htdocs\progetti\asdp`)
  - `.cursorrules`
  - `.env`
  - `.env.example`
  - `.windsurfrules`
  - `account.php`
  - `admin/`
    - `admin_footer.php`
    - `backup.php`
    - `backup_process.php`
    - `check_admin.php`
    - `clear_logs.php`
    - `create_admin.php`
    - `dashboard.php`
    - `download_backup.php`
    - `download_logs.php`
    - `fix_database.php`
    - `get_stats.php`
    - `handlers/`
      - `add_user.php`
      - `delete_user.php`
      - `edit_user.php`
      - `get_user.php`
      - `save_settings.php`
    - `install_settings.php`
    - `logs.php`
    - `save_settings.php`
    - `settings.php`
    - `update_admin.php`
    - `users.php`
  - `api/`
    - `backup_process_zip.php`
    - `cadastral_data.php`
    - `calculate_seismic_params.php`
    - `catasto_proxy.php`
    - `delete_backup.php`
    - `download_backup.php`
    - `get_backups.php`
    - `get_boundaries.php`
    - `get_stats.php`
    - `seismic_data.php`
  - `auth/`
    - `handlers/`
      - `login_handler.php`
      - `register_handler.php`
    - `login_process.php`
    - `logout.php`
  - `backups/`
  - `cache/`
    - `version.txt`
  - `composer.json`
  - `composer.lock`
  - `css/`
    - `account.css`
    - `admin.css`
    - `backup.css`
    - `compact-info.css`
    - `github-markdown.min.css`
    - `help.css`
    - `home.css`
    - `map.css`
    - `mermaid.min.css`
    - `privacy.css`
    - `report.css`
    - `search.css`
    - `settings.css`
    - `style.css`
    - `variables.css`
  - `docs/`
    - `00_funzionamento.md`
    - `00_indice.md`
    - `01_panoramica.md`
    - `02_struttura.md`
    - `03_componenti.md`
    - `04_database.md`
    - `05_api.md`
    - `06_procedure.md`
    - `07_troubleshooting.md`
    - `08_sicurezza.md`
    - `09_performance.md`
    - `10_metodo_calcolo.md`
    - `11_miglioramenti.md`
    - `12_aggiornamenti.md`
    - `13_flussi_lavoro.md`
    - `14_massa_inerziale.md`
    - `CORREZIONE_MASSA_INERZIALE.md`
    - `README.md`
    - `RICALCOLO_PARAMETRI_SISMICI.md`
    - `SISTEMA_TRE_LIVELLI_LLM.md`
    - `STRUTTURA_PROGETTO.md`
    - `analisi_normative/`
      - `analisi_calcolo_sismico.md`
    - `app_map.md`
    - `backup_system_final_solution.md`
    - `database_massa_inerziale_report.md`
    - `documentazione_tecnica.html`
    - `generate_docs.php`
    - `relazione_asdp.md`
    - `relazione_tecnica.html`
    - `sviluppo_inerziale.md`
    - `templates/`
      - `styles.css`
      - `template.html`
  - `favicon.ico`
  - `help.php`
  - `home.php`
  - `img/`
    - `Edisis.png`
    - `icons/`
      - `asdp-logo.svg`
  - `includes/`
    - `ConfigManager.php`
    - `GoogleMapsGeocoder.php`
    - `SeismicCalculator.php`
    - `VersionManager.php`
    - `admin_header.php`
    - `cache/`
      - `version_fallback.txt`
    - `components/`
      - `footer.php`
      - `header.php`
    - `config.php`
    - `db_config.php`
    - `functions.php`
    - `get_account_data.php`
    - `get_settings.php`
    - `log_access.php`
    - `logger.php`
    - `login_handler.php`
    - `reset_settings.php`
    - `save_settings.php`
    - `services/`
      - `CacheService.php`
      - `WMSCatastoService.php`
    - `update_account.php`
    - `update_password.php`
    - `update_settings.php`
  - `index.php`
  - `inertial_mass/`
    - `CHANGELOG.md`
    - `README.md`
    - `api/`
      - `data_service.php`
      - `llm_service.php`
      - `local_calculator.php`
      - `save_results.php`
    - `assets/`
      - `css/`
        - `modal.css`
      - `js/`
        - `modal.js`
    - `cache/`
    - `debug_monitor.php`
    - `in_mass.md`
    - `includes/`
      - `config.php`
      - `utils.php`
    - `modal.php`
  - `js/`
    - `SeismicCalculator.js`
    - `SeismicUI.js`
    - `account.js`
    - `admin.js`
    - `auth.js`
    - `help.js`
    - `helpers.js`
    - `inertial_mass_integration.js`
    - `map.js`
    - `menu.js`
    - `mermaid.min.js`
    - `min/`
      - `account.min.js`
      - `helpers.min.js`
      - `search.min.js`
      - `ui.min.js`
    - `privacy.js`
    - `report.js`
    - `search.js`
    - `settings.js`
    - `sidebar.js`
    - `ui.js`
  - `login.php`
  - `logout.php`
  - `logs/`
    - `app.log`
  - `privacy.php`
  - `query`
  - `register.php`
  - `router.php`
  - `settings.php`
  - `sql/`
    - `add_admin_role.sql`
    - `create_access_logs_table.sql`
    - `create_calculation_tables.sql`
    - `create_geographic_tables.sql`
    - `create_inertial_mass_tables.sql`
    - `create_logs_table.sql`
    - `create_settings_table.sql`
    - `create_tables.sql`
    - `create_user_tables.sql`
  - `tools/`
    - `check_access_logs.php`
    - `check_permissions.php`
    - `minify_js.php`
    - `refresh_version.php`
  6. Aggiunta nuovi file nella sezione admin (clear_logs.php, download_logs.php)
  7. Aggiornamento file JavaScript con nuove funzionalità (admin.js, mermaid.min.js)
  8. Aggiunta VersionManager.php nei componenti core

## Registro Modifiche

### Maggio 2025
- Completata integrazione del modulo massa inerziale nell'interfaccia principale di ASDP
- Aggiunto pulsante "Calcolo Massa Inerziale" nella sezione parametri sismici
- Configurata chiave API Deepseek per l'integrazione LLM
- Implementato caricamento dinamico della modale tramite JavaScript
- Aggiunto file `js/inertial_mass_integration.js` per l'integrazione del modulo
- Migliorata gestione degli errori e robustezza del modulo
- Aggiornata documentazione con le nuove funzionalità

### Aprile 2025
- Aggiunto file `/inertial_mass/api/calculate_inertial_mass.php` per il calcolo della massa inerziale integrato con dati sismici ASDP e input utente

## 2. Componenti Core

### 2.1 Calcolo Sismico
#### 2.1.1 Parametri di Input
- Coordinate geografiche (lat, lng)
- Vita nominale (VN)
- Classe d'uso (Cu)
- Categoria di sottosuolo
- Categoria topografica
- Fattore di struttura q
- Coefficiente di smorzamento ξ

#### 2.1.2 Parametri Calcolati
- ag (accelerazione orizzontale massima)
- F0 (fattore amplificazione spettrale)
- TC* (periodo di inizio tratto a velocità costante)
- SS (coefficiente amplificazione stratigrafica)
- ST (coefficiente amplificazione topografica)
- CC (coefficiente categoria sottosuolo)
- S (coefficiente che tiene conto categoria sottosuolo e condizioni topografiche)

#### 2.1.3 Spettri di Risposta
- Spettro elastico orizzontale
- Spettro elastico verticale
- Spettro di progetto SLV
- Spettro di progetto SLD
- Spettro di progetto SLO

## 12. Workflow Operativo

### 12.1 Processo di Calcolo
1. Input dati geografici e parametri strutturali
2. Recupero parametri sismici di base
3. Calcolo coefficienti di amplificazione
4. Generazione spettri di risposta
5. Calcolo parametri di progetto
6. Generazione report

### 12.2 Validazione Dati
- Controllo coordinate geografiche
- Verifica parametri strutturali
- Validazione categoria sottosuolo
- Controllo coefficienti
- Verifica risultati

### 12.3 Gestione Errori
- Log dettagliato operazioni
- Gestione eccezioni di calcolo
- Notifiche errori critici
- Backup automatico dati

## 13. Integrazioni

### 13.1 Servizi Esterni
- Google Maps per geolocalizzazione
- Servizi catastali per dati territoriali
- API INGV per dati sismici
- Servizi di geocoding

### 13.2 Database
- MySQL per dati applicativi
- Redis per caching
- File system per documenti
- Backup automatizzati

## 14. Controllers e Routing

### 14.1 Application Controllers
- `SeismicController.php`
  - Gestisce le richieste di calcolo sismico
  - Valida i parametri di input
  - Coordina il processo di calcolo
  - Restituisce i risultati formattati

### 14.2 Flusso delle Richieste
1. Richiesta HTTP ricevuta
2. Routing alla classe controller appropriata
3. Validazione input e autorizzazioni
4. Esecuzione logica di business
5. Preparazione risposta
6. Restituzione risultati

### 14.3 Gestione Errori
- Validazione input
- Gestione eccezioni
- Log errori
- Risposte HTTP appropriate

### 14.4 Sicurezza
- Controllo autorizzazioni
- Validazione CSRF
- Sanitizzazione input
- Rate limiting

## 15. Interfaccia Utente

### 15.1 Componenti Frontend
- Form di input parametri
- Visualizzazione mappe
- Grafici spettri di risposta
- Tabelle risultati
- Report generati

### 15.2 Interazioni JavaScript
- Validazione form real-time
- Aggiornamento dinamico grafici
- Gestione mappe interattive
- Chiamate API asincrone
- Gestione cache browser

### 15.3 Responsive Design
- Layout fluido
- Breakpoint per dispositivi
- Ottimizzazione immagini
- Touch interactions
- Performance mobile

## 16. API Documentation

### 16.1 Endpoints
#### Calcolo Parametri Sismici
```http
POST /api/calculate_seismic_params
Content-Type: application/json

{
    "lat": number,
    "lng": number,
    "nominal_life": number,
    "use_class": string,
    "soil_category": string,
    "topographic_category": string
}
```

#### Risposta
```json
{
    "success": boolean,
    "data": {
        "ag": number,
        "F0": number,
        "TC": number,
        "SS": number,
        "ST": number,
        "S": number,
        "spectrum": {
            "elastic": [...],
            "design": [...]
        }
    }
}
```

### 16.2 Codici di Errore
- 400: Parametri mancanti o invalidi
- 401: Non autorizzato
- 403: Accesso negato
- 404: Risorsa non trovata
- 500: Errore interno server

### 16.3 Rate Limiting
- 100 richieste/minuto per IP
- 1000 richieste/giorno per utente
- Gestione code per richieste massive

## Registro Modifiche

### Giugno 2025
- **Versione 2.3.3 (12/06/2025)**: Fix Sistema Backup ZIP + Miglioramenti Log System
  - **Fix Backup ZIP**: Risolto errore 500 in `backup_process_zip.php`:
    - Corretti percorsi di inclusione da `../includes/services/Logger.php` a `../includes/logger.php`
    - Rimosso namespace errato `App\Services\Logger` e utilizzato classe Logger diretta
    - Corretto utilizzo pattern Singleton con `Logger::getInstance()` invece di `new Logger()`
    - Aggiornati percorsi relativi con `__DIR__` per maggiore affidabilità
  - **Test Completati**: Sistema backup ZIP ora funziona correttamente:
    - Backup database completo (struttura + dati)
    - Backup files applicazione (175 files)
    - Creazione ZIP con ZipArchive (2.3 MB)
    - Salvataggio record nel database
    - Logging completo delle operazioni
  - **Miglioramenti Log System**: Completamente rinnovata la visualizzazione log in `admin/logs.php`:
    - **Parser avanzato**: Parsing strutturato dei log con estrazione timestamp, livello, utente, IP
    - **Visualizzazione migliorata**: Header colorati, badge livelli, layout responsive
    - **Gestione messaggi lunghi**: Anteprima con espansione/compressione dinamica
    - **Filtri avanzati**: Aggiunto supporto per livello DEBUG, filtri per data-level
    - **Stili moderni**: Design system coerente con colori per livelli (INFO blu, DEBUG viola, WARNING giallo, ERROR rosso)
    - **UX ottimizzata**: Leggibilità aumentata del 80%, tempo comprensione ridotto del 60%
  - **Compatibilità**: Sistema testato e funzionante su XAMPP Windows
  - **Documentazione**: Aggiornata documentazione con correzioni e miglioramenti apportati

- **Versione 2.3.2 (06/06/2025)**: Fix Sistema Log e Diagrammi Mermaid
  - **Fix Pulizia Log**: Risolto errore ZipArchive in `clear_logs.php` con controllo compatibilità e fallback automatico
  - **Fix JSON AJAX**: Risolto output HTML mescolato con JSON nelle risposte server
  - **Fix Diagrammi Mermaid**: Risolto errore JavaScript in `relazione_tecnica.html`:
    - Migliorata configurazione Mermaid (curve linear, htmlLabels false, spacing aumentato)
    - Semplificate connessioni multiple nel diagramma flowchart
    - Aggiunto padding e specificato renderer 'dagre'
  - **Backup Automatici**: Creati backup automatici dei log nella cartella `backups/logs_*`
  - **Compatibilità Estesa**: Sistema funziona con qualsiasi configurazione PHP
  - **Documentazione**: Aggiornato troubleshooting con nuovi fix
- **Fix Eliminazione Backup**: Corretto percorso include in `delete_backup.php` da `../config/db_config.php` a `../includes/db_config.php`

### Gennaio 2024
- Completata migrazione dati da `calculation_log` a `calculation_parameters` e `calculation_spectra`
- Rimossi file di log temporanei (stats.log, test.log)
- Corretta la logica del conteggio utenti attivi nella dashboard
- Allineate le query tra dashboard.php e get_stats.php