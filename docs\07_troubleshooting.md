# 7. Troubleshooting

## <PERSON><PERSON>ri Comuni

### 1. <PERSON>rrori di Connessione Database
```
Error: SQLSTATE[HY000] [1045] Access denied for user 'asdp_user'@'localhost'
```

**Soluzioni:**
1. Verificare credenziali in `.env`
2. Controllare utente MySQL:
   ```sql
   SELECT User, Host FROM mysql.user;
   ```
3. Ricreare utente:
   ```sql
   DROP USER 'asdp_user'@'localhost';
   CREATE USER 'asdp_user'@'localhost' IDENTIFIED BY 'password';
   GRANT ALL PRIVILEGES ON asdp_db.* TO 'asdp_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

### 2. Errori Mappa
```javascript
Error: Leaflet - Tiles not loading
```

**Soluzioni:**
1. Verificare connessione internet
2. Controllare API key in `js/map.js`
3. Pulire cache browser:
   ```powershell
   # Eliminare cache
   del /S /Q "%LocalAppData%\Google\Chrome\User Data\Default\Cache\*"
   ```
4. Verificare console browser (F12)

### 3. E<PERSON>ri Calcolo Sismico
```
Error: Invalid coordinates or parameters
```

**Soluzioni:**
1. Verificare formato coordinate:
   - Latitudine: -90 a +90
   - Longitudine: -180 a +180

2. Controllare parametri:
   ```php
   // Valori ammessi
   $vitaNominale = [50, 100];
   $classeUso = ['I', 'II', 'III', 'IV'];
   $categoriaTerreno = ['A', 'B', 'C', 'D', 'E'];
   ```

### 4. Errori Permessi File
```
Warning: Permission denied in /logs/error.log
```

**Soluzioni:**
1. Verificare permessi cartelle:
   ```powershell
   # Visualizza permessi
   icacls "C:\xampp\htdocs\asdp\logs"
   
   # Imposta permessi
   icacls "C:\xampp\htdocs\asdp\logs" /grant Users:(OI)(CI)F
   ```

2. Controllare proprietario:
   ```powershell
   # Cambia proprietario
   takeown /F "C:\xampp\htdocs\asdp\logs" /R
   ```

## Problemi frequenti e soluzioni

- **Dati obsoleti o errori inspiegabili**: Se il sistema mostra errori strani dopo aggiornamenti o import di database, eseguire una pulizia completa di database (svuotamento tabelle temporanee, log, backup) e rimuovere tutti i file temporanei/log dalle cartelle `backups`, `logs`, `cache`, `includes/logs`. (Vedi aggiornamento del 27/04/2025)

### 5. Problemi Modulo Massa Inerziale

#### 5.1 Raddoppio Icone nei Risultati (RISOLTO v2.1.0)
```
Sintomo: Icone duplicate nei titoli delle sezioni (📊📊 Distribuzione Forze, 🤖🤖 Analisi AI)
```

**Causa**: Problema di pulizia HTML nel rendering dinamico dei risultati

**Soluzione**:
1. **Aggiornamento automatico**: Il problema è stato risolto nella versione 2.1.0
2. **Verifica versione**: Controllare che il file `assets/js/modal.js` contenga la correzione:
   ```javascript
   // Riga ~858 in displayResults()
   resultsContent.innerHTML = ''; // Pulizia contenuto esistente
   const newHTML = generateResultsHTML(results);
   resultsContent.innerHTML = newHTML;
   ```

### 6. Problemi Sistema Log

#### 6.1 Errore 500 nella Pulizia Log (RISOLTO Giugno 2025)
```
Sintomo: Errore 500 Internal Server Error quando si tenta di pulire i log
Errore: Fatal error: Uncaught Error: Class "ZipArchive" not found
```

**Causa**: Estensione PHP ZIP non disponibile in XAMPP

**Soluzione**:
1. **Fix automatico**: Il problema è stato risolto con controllo compatibilità in `admin/clear_logs.php`
2. **Verifica fix**: Il sistema ora controlla automaticamente la disponibilità di ZipArchive:
   ```php
   if (class_exists('ZipArchive')) {
       // Crea backup ZIP
   } else {
       // Salva backup come directory
   }
   ```
3. **Abilitazione manuale ZIP** (opzionale):
   - Aprire `php.ini`
   - Decommentare: `extension=zip`
   - Riavviare Apache

#### 6.2 JSON Invalido nei Log (RISOLTO Giugno 2025)
```
Sintomo: Risposta non valida dal server durante pulizia log
```

**Causa**: Output HTML mescolato con JSON a causa dell'errore ZipArchive

**Soluzione**: Risolto automaticamente con il fix del punto 6.1

### 7. Problemi Diagrammi Mermaid

#### 7.1 Errore JavaScript Mermaid (RISOLTO Giugno 2025)
```
Sintomo: Could not find a suitable point for the given distance
File: docs/relazione_tecnica.html
```

**Causa**: Configurazione problematica e connessioni multiple complesse nel diagramma

**Soluzione**:
1. **Configurazione migliorata**:
   ```javascript
   mermaid.initialize({
     startOnLoad: false,
     flowchart: {
       curve: 'linear',        // Cambiato da 'basis'
       padding: 20,            // Aumentato da 10
       useMaxWidth: true,
       htmlLabels: false,      // Cambiato da true
       nodeSpacing: 100,       // Aumentato da 50
       rankSpacing: 100,       // Aumentato da 50
       diagramPadding: 20,     // Aggiunto
       defaultRenderer: 'dagre' // Specificato
     }
   });
   ```

2. **Connessioni semplificate**: Trasformate le connessioni multiple (D & E & F --> G) in connessioni singole (D --> G, E --> G, F --> G)

3. **Verifica funzionamento**: Il diagramma ora si renderizza correttamente senza errori JavaScript

3. **Cache browser**: Se il problema persiste, pulire la cache:
   ```powershell
   # Chrome
   del /S /Q "%LocalAppData%\Google\Chrome\User Data\Default\Cache\*"
   
   # Firefox
   del /S /Q "%AppData%\Mozilla\Firefox\Profiles\*\cache2\*"
   ```

#### 5.2 Errori API Deepseek
```
Error: Failed to fetch from LLM service
```

**Soluzioni**:
1. Verificare chiave API in `.env`:
   ```
   DEEPSEEK_API_KEY=***********************************
   ```

2. Controllare connessione:
   ```powershell
   curl -X POST "https://api.deepseek.com/v1/chat/completions" ^
        -H "Authorization: Bearer YOUR_API_KEY" ^
        -H "Content-Type: application/json"
   ```

3. Verificare rate limiting:
   ```sql
   SELECT COUNT(*) FROM inertial_mass_api_logs
   WHERE user_id = ? AND timestamp > DATE_SUB(NOW(), INTERVAL 1 MINUTE);
   ```

#### 5.3 Scroll Non Funzionante
```
Sintomo: Sezione risultati non scrollabile
```

**Soluzioni**:
1. Verificare CSS in `assets/css/modal.css`:
   ```css
   .modal-results {
       max-height: 70vh;
       overflow-y: auto;
   }
   ```

2. Controllare JavaScript:
   ```javascript
   // Verificare che l'elemento abbia l'ID corretto
   const resultsSection = document.getElementById('results-section');
   ```

#### 5.4 Calcolo Interrotto
```
Error: Calculation timeout or incomplete
```

**Soluzioni**:
1. Verificare timeout in `llm_service.php`:
   ```php
   $timeout = 180; // 3 minuti
   ```

2. Controllare log API:
   ```sql
   SELECT * FROM inertial_mass_api_logs
   WHERE status = 'error'
   ORDER BY timestamp DESC LIMIT 10;
   ```

3. Pulire cache se necessario:
   ```powershell
   del /S /Q "inertial_mass\cache\*"
   ```

#### 5.5 Debug Modulo Massa Inerziale
```javascript
// Abilitare debug nel browser
localStorage.setItem('inertial_mass_debug', 'true');

// Verificare stato modulo
console.log('Stato modulo:', inertialMassState);

// Controllare dati form
console.log('Dati form:', collectFormData());
```

**Log specifici**:
```powershell
# Log modulo massa inerziale
Get-Content -Path logs\inertial_mass.log -Tail 50

# Log API calls
Get-Content -Path logs\ai.log -Tail 20
```

## Debug

### 1. Log System

#### Abilitare Debug
```php
// .env
DEBUG=true
LOG_LEVEL=debug

// Esempio uso
Logger::debug('Test message', ['data' => $value]);
```

#### Visualizzare Log
```powershell
# Ultimi 50 errori
Get-Content -Path logs\error.log -Tail 50

# Log in tempo reale
Get-Content -Path logs\error.log -Wait
```

### 2. Database Debug

#### Query Log
```sql
-- Abilitare log query
SET GLOBAL general_log = 'ON';
SET GLOBAL general_log_file = 'C:/xampp/mysql/data/query.log';

-- Visualizzare query lente
SHOW VARIABLES LIKE '%slow%';
```

#### Analisi Query
```sql
-- Performance schema
SELECT * FROM performance_schema.events_statements_summary_by_digest
ORDER BY sum_timer_wait DESC LIMIT 10;
```

### 3. Frontend Debug

#### Console Browser
```javascript
// Abilitare debug
localStorage.setItem('debug', 'true');

// Log personalizzati
console.debug('Debug info:', data);
```

#### Network Analysis
1. Chrome DevTools (F12)
2. Tab Network
3. Filtrare per:
   - XHR
   - JS
   - CSS

### 4. API Debug

#### Test Endpoint
```powershell
# GET request
curl -X GET "http://localhost/asdp/api/cadastral_data.php?comune=Roma"

# POST request
curl -X POST "http://localhost/asdp/api/calculate_seismic_params.php" ^
     -H "Content-Type: application/json" ^
     -d "{\"lat\":41.9,\"lon\":12.5}"
```

#### Validazione Token
```php
// Decodifica token
$token = $_SERVER['HTTP_AUTHORIZATION'];
$decoded = JWT::decode($token, $key, array('HS256'));
```

## Procedure di Recovery

### 1. Database Corrotto
```powershell
# Backup dati
mysqldump -u root -p asdp_db > backup_before_repair.sql

# Riparare tabelle
mysqlcheck -u root -p --auto-repair asdp_db

# Se necessario, ripristinare
mysql -u root -p asdp_db < backup_before_repair.sql
```

### 2. File System Corrotto
```powershell
# Verificare integrità
chkdsk C: /f

# Ripristinare da backup
xcopy /E /I /Y backup\* C:\xampp\htdocs\asdp\
```

### 3. Cache Corrotta
```powershell
# Pulire tutte le cache
del /S /Q temp\cache\*
del /S /Q temp\sessions\*

# Riavviare servizi
net stop Apache2.4
net start Apache2.4
```

## Note Importanti

### Prevenzione
1. Backup regolari
2. Monitoraggio log
3. Test periodici
4. Aggiornamenti sistema

### Best Practices
1. Documentare errori
2. Mantenere log puliti
3. Verificare backup
4. Test di recovery