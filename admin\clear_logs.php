<?php
require_once '../includes/db_config.php';
require_once '../includes/Logger.php';
session_start();

// Verifica autenticazione
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['success' => false, 'error' => 'Accesso non autorizzato']);
    exit;
}

header('Content-Type: application/json');

try {
    $conn = getConnection();
    
    // Backup dei log prima della pulizia
    $backupDir = __DIR__ . '/../backups/logs_' . date('Y-m-d_H-i-s');
    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
    }
    
    // Backup dei file di log
    $logFiles = [
        'app.log',
        'seismic_calc.log',
        'error.log',
        'cadastral.log',
        'catasto_proxy.log'
    ];
    
    foreach ($logFiles as $logFile) {
        $sourcePath = __DIR__ . '/../logs/' . $logFile;
        if (file_exists($sourcePath)) {
            copy($sourcePath, $backupDir . '/' . $logFile);
            // Pulisci il file mantenendo solo l'intestazione
            file_put_contents($sourcePath, "");
        }
    }
    
    // Backup dei log del database
    $stmt = $conn->query("SELECT al.*, u.username 
                         FROM access_logs al 
                         LEFT JOIN users u ON al.user_id = u.id 
                         ORDER BY al.timestamp DESC");
    $dbLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Salva i log del database in un file CSV
    $csvFile = $backupDir . '/database_logs.csv';
    $fp = fopen($csvFile, 'w');
    
    // Intestazioni CSV
    fputcsv($fp, ['Data/Ora', 'Utente', 'Azione', 'IP', 'User Agent']);
    
    // Dati
    foreach ($dbLogs as $log) {
        fputcsv($fp, [
            $log['timestamp'],
            $log['username'] ?? 'N/A',
            $log['action'],
            $log['ip_address'],
            $log['user_agent']
        ]);
    }
    
    fclose($fp);
    
    // Pulisci la tabella dei log mantenendo solo gli ultimi 7 giorni
    $conn->exec("DELETE FROM access_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL 7 DAY)");
    
    // Verifica se ZipArchive è disponibile
    if (class_exists('ZipArchive')) {
        // Comprimi il backup
        $zipFile = $backupDir . '.zip';
        $zip = new ZipArchive();
        
        if ($zip->open($zipFile, ZipArchive::CREATE | ZipArchive::OVERWRITE) === TRUE) {
            $files = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($backupDir),
                RecursiveIteratorIterator::LEAVES_ONLY
            );
            
            foreach ($files as $file) {
                if (!$file->isDir()) {
                    $filePath = $file->getRealPath();
                    $relativePath = substr($filePath, strlen($backupDir) + 1);
                    $zip->addFile($filePath, $relativePath);
                }
            }
            
            $zip->close();
            
            // Rimuovi la directory temporanea
            array_map('unlink', glob("$backupDir/*.*"));
            rmdir($backupDir);
            
            echo json_encode([
                'success' => true,
                'message' => 'Log puliti con successo. Backup compresso salvato in: ' . basename($zipFile)
            ]);
        } else {
            throw new Exception('Impossibile creare il file ZIP di backup');
        }
    } else {
        // ZipArchive non disponibile, mantieni la directory di backup
        echo json_encode([
            'success' => true,
            'message' => 'Log puliti con successo. Backup salvato nella directory: ' . basename($backupDir) . ' (ZipArchive non disponibile)'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Errore nella pulizia dei log: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Errore durante la pulizia dei log'
    ]);
}