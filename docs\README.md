# Documentazione ASDP - Advanced Seismic Dissipator Project

## 📋 Panoramica

Questa cartella contiene tutta la documentazione tecnica e operativa del progetto ASDP (Advanced Seismic Dissipator Project), un sistema avanzato per il calcolo e l'analisi di parametri sismici secondo le normative NTC 2018.

## 🗂️ Struttura Documentazione

### 📚 Documentazione Base
- **[00_indice.md](00_indice.md)** - Indice generale di tutta la documentazione
- **[STRUTTURA_PROGETTO.md](STRUTTURA_PROGETTO.md)** - **NUOVO** - Struttura completa del progetto
- **[01_panoramica.md](01_panoramica.md)** - Introduzione e panoramica del progetto
- **[02_struttura.md](02_struttura.md)** - Struttura dell'applicazione
- **[03_componenti.md](03_componenti.md)** - Componenti principali del sistema

### 🔧 Documentazione Tecnica
- **[04_database.md](04_database.md)** - Struttura e gestione del database
- **[05_api.md](05_api.md)** - Documentazione delle API REST
- **[06_procedure.md](06_procedure.md)** - Procedure operative standard
- **[07_troubleshooting.md](07_troubleshooting.md)** - Risoluzione problemi comuni
- **[08_sicurezza.md](08_sicurezza.md)** - Misure di sicurezza implementate
- **[09_performance.md](09_performance.md)** - Ottimizzazione delle prestazioni
- **[10_metodo_calcolo.md](10_metodo_calcolo.md)** - Metodologie di calcolo sismico
- **[backup_system_final_solution.md](backup_system_final_solution.md)** - Sistema di backup finale

### 🏗️ Modulo Massa Inerziale
- **[14_massa_inerziale.md](14_massa_inerziale.md)** - Documentazione completa del modulo
- **[CORREZIONE_MASSA_INERZIALE.md](CORREZIONE_MASSA_INERZIALE.md)** - Correzioni e miglioramenti
- **[SISTEMA_TRE_LIVELLI_LLM.md](SISTEMA_TRE_LIVELLI_LLM.md)** - Sistema a tre livelli per LLM
- **[sviluppo_inerziale.md](sviluppo_inerziale.md)** - Note di sviluppo

### 🔄 Parametri Sismici
- **[RICALCOLO_PARAMETRI_SISMICI.md](RICALCOLO_PARAMETRI_SISMICI.md)** - Ricalcolo dinamico parametri

### 📊 Gestione Progetto
- **[11_miglioramenti.md](11_miglioramenti.md)** - Proposte di miglioramento
- **[12_aggiornamenti.md](12_aggiornamenti.md)** - Registro degli aggiornamenti
- **[13_flussi_lavoro.md](13_flussi_lavoro.md)** - Flussi di lavoro

### 📋 File di Supporto
- **[relazione_asdp.md](relazione_asdp.md)** - Relazione generale del progetto
- **[app_map.md](app_map.md)** - Mappa dell'applicazione
- **[database_massa_inerziale_report.md](database_massa_inerziale_report.md)** - Report database

### 📁 Directory Speciali
- **[analisi_normative/](analisi_normative/)** - Analisi delle normative tecniche NTC 2018
- **[templates/](templates/)** - Template per documentazione

## 🚀 Funzionalità Principali

### Sistema a Tre Livelli LLM
Il progetto implementa un sistema innovativo di calcolo massa inerziale con fallback automatico:

1. **🥇 Deepseek AI** (Primario) - Analisi ingegneristica avanzata
2. **🥈 Google Gemma3** (Fallback) - Modello compatto e veloce  
3. **🥉 Calcolo Locale** (Garantito) - Formule NTC 2018 standard

**Affidabilità**: 99.9% garantita con fallback automatico trasparente.

### Calcolo Parametri Sismici Dinamici
- Calcolo TR (periodo di ritorno) basato su vita nominale e classe d'uso
- Interpolazione automatica da griglia sismica nazionale
- Coefficienti di amplificazione SS, CC, ST dinamici
- Integrazione completa con database sismico

### Interfaccia Utente Avanzata
- Modal responsive con animazioni fluide
- Caricamento professionale con progress bar
- Gestione errori robusta con retry automatico
- Risultati eleganti con visualizzazione ottimizzata

## 📈 Versioni Recenti

### v2.3.2 (06/06/2025) - Fix Sistema Log e Diagrammi Mermaid
- ✅ **FIX PULIZIA LOG**: Risolto errore ZipArchive con controllo compatibilità automatico
- ✅ **FIX JSON AJAX**: Risolto output HTML mescolato con JSON nelle risposte server
- ✅ **FIX DIAGRAMMI MERMAID**: Risolto errore JavaScript con configurazione migliorata
- ✅ **BACKUP AUTOMATICI**: Creazione backup log prima della pulizia
- ✅ **COMPATIBILITÀ ESTESA**: Sistema funziona con qualsiasi configurazione PHP
- ✅ **DOCUMENTAZIONE**: Aggiornato troubleshooting con nuovi fix

### v2.3.0 (05/06/2025) - Pulizia Completa Workspace
- ✅ **PULIZIA WORKSPACE**: Eliminati file obsoleti, duplicati e di test
- ✅ **STRUTTURA OTTIMIZZATA**: Riorganizzata architettura progetto
- ✅ **DOCUMENTAZIONE AGGIORNATA**: Creato STRUTTURA_PROGETTO.md completo
- ✅ **PERFORMANCE**: Rimossi file non necessari per migliori prestazioni
- ✅ **MANUTENIBILITÀ**: Struttura più pulita e navigabile

### v2.2.0 (05/06/2025) - Sistema a Tre Livelli LLM
- ✅ Implementato sistema fallback Deepseek → Gemma3 → Locale
- ✅ Corretti bug parametri sismici dinamici
- ✅ Organizzata documentazione in cartella docs
- ✅ Affidabilità 99.9% garantita

### v2.1.0 (04/06/2025) - Correzioni Critiche
- ✅ Risolto problema raddoppio icone
- ✅ Ottimizzate performance rendering
- ✅ Implementata pulizia HTML dinamica

## 🔍 Come Navigare la Documentazione

1. **Inizia da**: [00_indice.md](00_indice.md) per una panoramica completa
2. **Per sviluppatori**: Consulta [02_struttura.md](02_struttura.md) e [03_componenti.md](03_componenti.md)
3. **Per amministratori**: Vedi [04_database.md](04_database.md) e [08_sicurezza.md](08_sicurezza.md)
4. **Per troubleshooting**: Consulta [07_troubleshooting.md](07_troubleshooting.md)
5. **Per aggiornamenti**: Monitora [12_aggiornamenti.md](12_aggiornamenti.md)

## 📞 Supporto

Per domande tecniche o problemi:
1. Consulta prima [07_troubleshooting.md](07_troubleshooting.md)
2. Verifica [12_aggiornamenti.md](12_aggiornamenti.md) per problemi noti
3. Controlla [STRUTTURA_PROGETTO.md](STRUTTURA_PROGETTO.md) per architettura

## 📝 Contribuire alla Documentazione

- Registra modifiche in [12_aggiornamenti.md](12_aggiornamenti.md)
- Aggiorna [00_indice.md](00_indice.md) per nuovi documenti
- Usa template in [templates/](templates/) per consistenza
- Mantieni aggiornato [STRUTTURA_PROGETTO.md](STRUTTURA_PROGETTO.md) per modifiche architetturali

---

**Ultimo aggiornamento**: 06/06/2025
**Versione documentazione**: 2.3.2
**Stato progetto**: ✅ Sistema Log e Diagrammi Mermaid risolti - Piena operatività garantita
