<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relazione Tecnica ASDP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <style>
    :root {
        --primary-color: #FF7043;
        --bg-color: #1E1E1E;
        --text-color: #FFFFFF;
        --border-color: #333333;
        --hover-color: #FF8A65;
    }

            body {
                margin: 0;
                padding: 0;
        background-color: var(--bg-color);
        color: var(--text-color);
        font-family: 'Segoe UI', Arial, sans-serif;
        display: flex;
    }

    /* Sidebar per l'indice */
    .sidebar {
        width: 300px;
        height: 100vh;
        background: var(--bg-color);
        border-right: 1px solid var(--border-color);
        padding: 20px;
        position: fixed;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: var(--primary-color) var(--bg-color);
    }

    .sidebar::-webkit-scrollbar {
        width: 8px;
    }

    .sidebar::-webkit-scrollbar-track {
        background: var(--bg-color);
    }

    .sidebar::-webkit-scrollbar-thumb {
        background-color: var(--primary-color);
        border-radius: 4px;
    }

    .sidebar-header {
        padding: 10px 0;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--primary-color);
        font-size: 1.2em;
        font-weight: bold;
    }

    .toc {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .toc li {
        margin: 8px 0;
        padding-left: 15px;
        border-left: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .toc li:hover {
        border-left-color: var(--primary-color);
    }

    .toc a {
        color: var(--text-color);
        text-decoration: none;
        transition: color 0.3s ease;
        display: block;
        padding: 5px 0;
    }

    .toc a:hover {
        color: var(--primary-color);
    }

    .toc a.active {
        color: var(--primary-color);
        font-weight: bold;
    }

    /* Contenuto principale */
    .main-content {
        margin-left: 300px;
        padding: 40px;
        flex: 1;
        background-color: #FFFFFF;
        color: #000000;
        min-height: 100vh;
    }

    .markdown-body {
        box-sizing: border-box;
        min-width: 200px;
        max-width: 980px;
        margin: 0 auto;
        padding: 45px;
    }

    /* Stili per le sezioni della documentazione */
    .section-header {
        margin-bottom: 2rem;
    }

    .section-header h1 {
        color: var(--primary-color);
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }

    .section-header p {
        color: #666;
        font-style: italic;
    }

    .section-content {
        margin-bottom: 3rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .doc-section {
        margin: 2rem 0;
        padding: 1.5rem;
        background: white;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .doc-section h3 {
        color: var(--primary-color);
        margin-top: 0;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #eee;
    }

    /* Stili per il codice e i blocchi di codice */
    .code-block {
        background: #2d2d2d;
        color: #e0e0e0;
        padding: 1rem;
        border-radius: 6px;
        margin: 1rem 0;
        font-family: 'Consolas', 'Monaco', monospace;
        overflow-x: auto;
    }

    .code-inline {
        background: #2d2d2d;
        color: #e0e0e0;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-family: 'Consolas', 'Monaco', monospace;
    }

    /* Stili per i log */
    .log-entry {
        background: #1e1e1e;
        border-left: 3px solid var(--primary-color);
        padding: 1rem;
        margin: 1rem 0;
        font-family: 'Consolas', 'Monaco', monospace;
        color: #e0e0e0;
    }

    /* Stili per le note tecniche */
    .technical-note {
        background: #fff8dc;
        border-left: 4px solid #ffd700;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 6px 6px 0;
    }

    /* Stili per le tabelle */
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
    }

    th, td {
        padding: 0.75rem;
        border: 1px solid #ddd;
    }

    th {
        background: #f5f5f5;
        font-weight: 600;
    }

    tr:nth-child(even) {
        background: #f9f9f9;
    }

    /* Stili per i link */
    a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.3s ease;
    }

    a:hover {
        color: var(--hover-color);
        text-decoration: underline;
    }

    /* Stili per le note a piè di pagina */
    .footnotes {
        margin-top: 3rem;
        padding-top: 1rem;
        border-top: 1px solid #ddd;
    }

    .footnote {
        font-size: 0.8em;
        vertical-align: super;
    }

    .footnote-backref {
        font-size: 0.8em;
        text-decoration: none;
    }

    /* Pulsanti */
    .print-button {
        position: fixed;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1000;
        bottom: 30px;
        right: 30px;
    }

    .close-button {
        position: fixed;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1000;
        top: 20px;
        right: 20px;
    }

    .print-button:hover, .close-button:hover {
        background: var(--hover-color);
        transform: scale(1.1);
        box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    }

        @media print {
        .sidebar, .print-button, .close-button {
            display: none;
        }

        .main-content {
            margin-left: 0;
                padding: 0;
        }

        .markdown-body {
            padding: 20px;
        }

        .section-content {
            box-shadow: none;
                background: none;
            }

        .doc-section {
            box-shadow: none;
            border: 1px solid #ddd;
        }

        .code-block, .code-inline {
            background: #f8f8f8;
            color: #333;
            border: 1px solid #ddd;
        }

        .log-entry {
            background: #f8f8f8;
            color: #333;
            border-left: 2px solid #666;
        }
    }

    /* Stili per i diagrammi Mermaid */
    .mermaid {
        margin: 2rem 0;
        padding: 2rem;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: auto;
    }

    .mermaid svg {
        max-width: 100%;
        height: auto !important;
        display: block;
        margin: 0 auto;
    }

    .mermaid .node rect,
    .mermaid .node circle,
    .mermaid .node ellipse,
    .mermaid .node polygon {
        fill: #ffffff;
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .node.clickable {
        cursor: pointer;
    }

    .mermaid .edgeLabel {
        background-color: #ffffff;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .mermaid .edgePath {
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .edgePath .path {
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .cluster rect {
        fill: #f8f9fa;
        stroke: var(--primary-color);
        stroke-width: 1px;
    }

    .mermaid .cluster text {
        font-size: 14px;
        font-weight: 600;
    }

    @media print {
        .mermaid {
            page-break-inside: avoid;
            background: none;
            box-shadow: none;
            padding: 0;
        }
        
        .mermaid svg {
            max-width: 100%;
            height: auto !important;
        }
    }
</style><style>
    .doc-section.active {
        border-left: 4px solid var(--primary-color);
        padding-left: calc(1.5rem - 4px);
    }
    
    .toc a.active {
        color: var(--primary-color);
        font-weight: bold;
        border-left: 2px solid var(--primary-color);
        margin-left: -2px;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-book"></i> Indice
        </div>
        <ul class="toc" id="toc"></ul>
    </div>
    
    <div class="main-content">
        <div class="markdown-body" id="content">
            <div class='executive-summary'>
<h1>Relazione Tecnica A.S.D.P.</h1>
<p>Advanced Seismic Dissipator Project</p>
<p>Sistema Intelligente per la Selezione e Configurazione di Dissipatori Sismici Magnetoreologici</p>
</div>

<h1>Relazione sul Progetto A.S.D.P. - Advanced Seismic Dissipator Project</h1>
<h2>Introduzione</h2>
<p>Il progetto A.S.D.P. (Advanced Seismic Dissipator Project) nasce dalla necessità di fornire uno strumento avanzato e integrato per l'analisi sismica e la gestione dei dati relativi alle strutture edilizie. In un paese come l'Italia, caratterizzato da un'elevata sismicità e da un patrimonio edilizio variegato, disporre di strumenti efficaci per la valutazione del rischio sismico è di fondamentale importanza.</p>
<p>Progetto A.S.D.P. - Advanced Seismic Dissipator Project serve ad individuare i parametri sismici di un edificio al fine di utilizzare un dissipatore sismico magnetoreologico controllato da AI. L'individuazione dei parametri sismici consente la scelta del dissipatore più appropriato.</p>
<h2>Finalità del Progetto</h2>
<h3>Obiettivi Principali</h3>
<ol>
<li>
<p><strong>Analisi Sismica Avanzata</strong></p>
<ul>
<li>Valutazione precisa dei parametri sismici secondo le NTC 2018</li>
<li>Calcolo degli spettri di risposta per diverse condizioni del terreno</li>
<li>Analisi dell'interazione terreno-struttura</li>
<li>Valutazione degli effetti di amplificazione locale</li>
</ul>
</li>
<li>
<p><strong>Gestione Dati Territoriali</strong></p>
<ul>
<li>Integrazione con database catastali</li>
<li>Mappatura dettagliata delle zone sismiche</li>
<li>Gestione di dati geologici e geotecnici</li>
<li>Archiviazione di informazioni storiche sugli eventi sismici</li>
</ul>
</li>
<li>
<p><strong>Supporto alla Progettazione</strong></p>
<ul>
<li>Strumenti per il dimensionamento di sistemi di dissipazione</li>
<li>Valutazione dell'efficacia degli interventi di miglioramento</li>
<li>Analisi costi-benefici degli interventi proposti</li>
<li>Generazione di report tecnici dettagliati</li>
</ul>
</li>
</ol>
<h2>Fasi di Sviluppo</h2>
<h3>Fase 1: Analisi e Pianificazione</h3>
<p>La prima fase del progetto ha visto un'intensa attività di ricerca e analisi:</p>
<ul>
<li>Studio approfondito delle normative vigenti</li>
<li>Analisi delle esigenze degli utenti finali</li>
<li>Valutazione delle tecnologie disponibili</li>
<li>Definizione dell'architettura del sistema</li>
</ul>
<h3>Fase 2: Sviluppo del Core</h3>
<p>Lo sviluppo del nucleo applicativo ha comportato:</p>
<ul>
<li>Implementazione degli algoritmi di calcolo sismico</li>
<li>Creazione del sistema di gestione dati</li>
<li>Sviluppo dell'interfaccia utente</li>
<li>Integrazione con servizi esterni (mappe, database catastali)</li>
</ul>
<h3>Fase 3: Testing e Validazione</h3>
<p>Questa fase ha incluso:</p>
<ul>
<li>Test approfonditi degli algoritmi di calcolo</li>
<li>Verifiche di usabilità dell'interfaccia</li>
<li>Validazione dei risultati con casi studio reali</li>
<li>Ottimizzazione delle performance</li>
</ul>
<h3>Fase 4: Implementazione e Deployment</h3>
<p>L'ultima fase ha visto:</p>
<ul>
<li>Rilascio della versione beta a utenti selezionati</li>
<li>Raccolta e implementazione dei feedback</li>
<li>Ottimizzazione delle funzionalità</li>
<li>Preparazione della documentazione tecnica</li>
</ul>
<h2>Stato Attuale e Prospettive Future</h2>
<h3>Risultati Raggiunti</h3>
<ul>
<li>Sviluppo di un'interfaccia user-friendly e intuitiva</li>
<li>Implementazione di algoritmi di calcolo affidabili</li>
<li>Creazione di un sistema di gestione dati robusto</li>
<li>Integrazione con servizi esterni essenziali</li>
</ul>
<h3>Sviluppi Futuri</h3>
<ol>
<li>
<p><strong>Espansione Funzionalità</strong></p>
<ul>
<li>Implementazione di nuovi modelli di calcolo</li>
<li>Aggiunta di funzionalità di machine learning</li>
<li>Sviluppo di moduli per l'analisi predittiva</li>
<li>Integrazione con sistemi BIM</li>
</ul>
</li>
<li>
<p><strong>Miglioramenti Tecnici</strong></p>
<ul>
<li>Ottimizzazione delle performance</li>
<li>Implementazione di nuove API</li>
<li>Miglioramento della sicurezza</li>
<li>Espansione della compatibilità con diversi dispositivi</li>
</ul>
</li>
<li>
<p><strong>Espansione del Mercato</strong></p>
<ul>
<li>Localizzazione per mercati internazionali</li>
<li>Sviluppo di versioni specializzate</li>
<li>Creazione di partnership strategiche</li>
<li>Espansione della base utenti</li>
</ul>
</li>
</ol>
<h2>Conclusioni</h2>
<p>Il progetto A.S.D.P. rappresenta un importante passo avanti nella gestione del rischio sismico, offrendo uno strumento completo e affidabile per professionisti del settore. Le prospettive future indicano un potenziale di crescita significativo, con possibilità di espansione sia in termini di funzionalità che di mercato.</p>
<p>La continua evoluzione delle normative e delle tecnologie richiederà un costante aggiornamento del sistema, ma la solida base sviluppata finora fornisce un'eccellente piattaforma per gli sviluppi futuri. L'obiettivo rimane quello di contribuire alla sicurezza sismica del patrimonio edilizio, fornendo strumenti sempre più efficaci per la progettazione e la valutazione degli interventi di miglioramento sismico.</p>
<div class="flowchart">
<h3>Flusso di Funzionamento del Sistema ASDP</h3>
<div class="mermaid">
graph TD
    A[Utente] --> B[Dashboard]
    B --> C[Form Edificio]
    C --> D[Parametri Strutturali]
    C --> E[Caratteristiche Sismiche]
    C --> F[Dati Ambientali]
    D --> G[Elaborazione IA]
    E --> G
    F --> G
    G --> H[Analisi Spettrale]
    G --> I[Parametri Dinamici]
    H --> J[Selezione Dissipatore]
    I --> J
    J --> K[Simulazione Risposta]
    J --> L[Parametri MR]
    K --> M[Report Tecnico]
    L --> M
    M --> N[Specifiche Dissipatore]
    M --> O[Grafici Prestazionali]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#bbf,stroke:#333,stroke-width:2px
    style J fill:#bfb,stroke:#333,stroke-width:2px
    style M fill:#fbb,stroke:#333,stroke-width:2px

    classDef aiNode fill:#ffebcd,stroke:#333,stroke-width:2px
    class G,K aiNode
</div>
</div><h2>Panoramica del Sistema</h2>
<p>Il progetto A.S.D.P. (Advanced Seismic Dissipator Project) rappresenta un'innovativa soluzione per la protezione sismica degli edifici attraverso l'utilizzo di dissipatori magnetoreologici controllati da intelligenza artificiale. Il sistema è progettato per analizzare le caratteristiche strutturali e sismiche dell'edificio, determinare i parametri ottimali e selezionare il dissipatore più appropriato per garantire la massima protezione sismica.</p>
<h3>Componenti Principali del Sistema</h3>
<ol>
<li><strong>Analisi Strutturale</strong>: Valutazione dettagliata delle caratteristiche dell'edificio, inclusi parametri geometrici, materiali e comportamento dinamico.</li>
<li><strong>Elaborazione IA</strong>: Un sistema di intelligenza artificiale analizza i dati strutturali e sismici per determinare la configurazione ottimale del dissipatore.</li>
<li><strong>Sistema Magnetoreologico</strong>: Dissipatori avanzati che utilizzano fluidi magnetoreologici per adattare dinamicamente la risposta alle sollecitazioni sismiche.</li>
<li><strong>Controllo Adattivo</strong>: Sistema di controllo in tempo reale che modifica le proprietà del dissipatore in base all'input sismico.</li>
</ol>
<h3>Processo di Analisi e Selezione</h3>
<ol>
<li><strong>Acquisizione Dati</strong>: Raccolta dei parametri strutturali dell'edificio e delle caratteristiche sismiche del sito.</li>
<li><strong>Analisi Spettrale</strong>: Elaborazione dello spettro di risposta specifico per il sito e la struttura.</li>
<li><strong>Ottimizzazione IA</strong>: L'intelligenza artificiale determina i parametri ottimali del dissipatore basandosi su multiple simulazioni.</li>
<li><strong>Configurazione Sistema</strong>: Definizione delle specifiche tecniche del dissipatore e dei parametri di controllo.</li>
<li><strong>Validazione</strong>: Simulazioni numeriche per verificare l'efficacia del sistema proposto.</li>
</ol>
<h2>Metodologia di Analisi Sismica</h2>
<h1>Metodo di Calcolo Sismico</h1>
<p>Ultimo aggiornamento: 20/01/2024</p>
<h2>1. Introduzione</h2>
<p>Il calcolo sismico viene effettuato secondo le NTC 2018 (Norme Tecniche per le Costruzioni) e relative circolari applicative. Il processo si articola in diverse fasi che vengono eseguite in sequenza.</p>
<h2>2. Parametri di Input</h2>
<h3>2.1 Parametri Geografici</h3>
<ul>
<li>Latitudine (LAT)</li>
<li>Longitudine (LNG)</li>
<li>Categoria di sottosuolo</li>
<li>Categoria topografica</li>
</ul>
<h3>2.2 Parametri Strutturali</h3>
<ul>
<li>Vita nominale di progetto (VN)</li>
<li>Classe d'uso (Cu)</li>
<li>Periodo di riferimento (VR = VN × Cu)</li>
<li>Fattore di struttura (q)</li>
<li>Coefficiente di smorzamento (ξ)</li>
</ul>
<h2>3. Fasi di Calcolo</h2>
<h3>3.1 Determinazione Parametri di Base</h3>
<ol>
<li>Recupero ag, F0, TC* dai dati di riferimento</li>
<li>Interpolazione valori per il punto specifico</li>
<li>Calcolo periodo di ritorno TR</li>
</ol>
<h3>3.2 Coefficienti di Amplificazione</h3>
<ol>
<li>Coefficiente stratigrafico SS</li>
<li>Coefficiente topografico ST</li>
<li>Coefficiente S = SS × ST</li>
<li>Coefficiente CC per categoria sottosuolo</li>
</ol>
<h3>3.3 Periodi Caratteristici</h3>
<ol>
<li>TC = CC × TC*</li>
<li>TB = TC / 3</li>
<li>TD = 4.0 × (ag/g) + 1.6</li>
</ol>
<h2>4. Spettri di Risposta</h2>
<h3>4.1 Spettro Elastico Orizzontale</h3>
<p>Per 0 ≤ T &lt; TB:</p>
<p>Per TB ≤ T &lt; TC:</p>
<p>Per TC ≤ T &lt; TD:</p>
<p>Per TD ≤ T:</p>
<h3>4.2 Spettro di Progetto</h3>
<p>Ottenuto riducendo le ordinate dello spettro elastico mediante il fattore q:</p>
<h2>5. Validazione Risultati</h2>
<h3>5.1 Controlli Automatici</h3>
<ol>
<li>Verifica range parametri di input</li>
<li>Controllo coerenza risultati</li>
<li>Validazione spettri generati</li>
<li>Verifica limiti normativi</li>
</ol>
<h3>5.2 Verifiche Manuali</h3>
<ol>
<li>Confronto con casi noti</li>
<li>Verifica andamento spettri</li>
<li>Controllo valori caratteristici</li>
<li>Validazione coefficienti</li>
</ol>
<h2>6. Output Generati</h2>
<h3>6.1 Parametri Calcolati</h3>
<ul>
<li>ag: accelerazione orizzontale massima</li>
<li>F0: fattore amplificazione spettrale</li>
<li>TC*: periodo inizio tratto velocità costante</li>
<li>SS: coefficiente amplificazione stratigrafica</li>
<li>ST: coefficiente amplificazione topografica</li>
<li>S: coefficiente che tiene conto categoria sottosuolo</li>
</ul>
<h3>6.2 Spettri</h3>
<ul>
<li>Spettro elastico orizzontale</li>
<li>Spettro elastico verticale</li>
<li>Spettro di progetto SLV</li>
<li>Spettro di progetto SLD</li>
<li>Spettro di progetto SLO</li>
</ul>
<h2>7. Ottimizzazioni</h2>
<h3>7.1 Performance</h3>
<ol>
<li>Caching risultati frequenti</li>
<li>Ottimizzazione calcoli</li>
<li>Parallelizzazione processi</li>
<li>Gestione memoria</li>
</ol>
<h3>7.2 Precisione</h3>
<ol>
<li>Interpolazione dati precisa</li>
<li>Arrotondamenti controllati</li>
<li>Validazione step-by-step</li>
<li>Gestione casi limite</li>
</ol>
<h2>Note Tecniche</h2>
<ol>
<li>Tutti i calcoli vengono eseguiti in doppia precisione</li>
<li>I risultati vengono arrotondati solo nella presentazione finale</li>
<li>Le interpolazioni utilizzano il metodo bilineare</li>
<li>I grafici vengono generati con precisione 0.01s</li>
</ol>
<h2>Riferimenti Normativi</h2>
<ul>
<li>NTC 2018 (D.M. 17/01/2018)</li>
<li>Circolare applicativa n.7 del 21/01/2019</li>
<li>Eurocodice 8 (EN 1998-1)</li>
</ul>
<h2>Stati Limite</h2>
<h3>SLO (Stato Limite di Operatività)</h3>
<ul>
<li><strong>Probabilità</strong>: 81% in VR</li>
<li><strong>Periodo</strong>: TR = 30 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione nel suo complesso non deve subire danni ed interruzioni d'uso significative.</li>
</ul>
<h3>SLD (Stato Limite di Danno)</h3>
<ul>
<li><strong>Probabilità</strong>: 63% in VR</li>
<li><strong>Periodo</strong>: TR = 50 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione nel suo complesso subisce danni tali da non mettere a rischio gli utenti.</li>
</ul>
<h3>SLV (Stato Limite di salvaguardia della Vita)</h3>
<ul>
<li><strong>Probabilità</strong>: 10% in VR</li>
<li><strong>Periodo</strong>: TR = 475 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione subisce rotture e crolli dei componenti non strutturali ed impiantistici.</li>
</ul>
<h3>SLC (Stato Limite di prevenzione del Collasso)</h3>
<ul>
<li><strong>Probabilità</strong>: 5% in VR</li>
<li><strong>Periodo</strong>: TR = 975 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione subisce gravi rotture e crolli dei componenti non strutturali ed impiantistici.</li>
</ul>
<h2>Parametri Input</h2>
<h3>1. Vita Nominale (VN)</h3>
<h3>2. Classe d'Uso (CU)</h3>
<h3>3. Categoria Sottosuolo</h3>
<h3>4. Categoria Topografica</h3>
<h2>Formule</h2>
<h3>1. Periodo di Riferimento (VR)</h3>
<h3>2. Tempo di Ritorno (TR)</h3>
<h3>3. Accelerazione al Suolo (ag)</h3>
<h3>4. Coefficienti di Amplificazione</h3>
<h3>5. Spettro di Risposta Elastico</h3>
<h2>Esempio di Calcolo</h2>
<h3>Input</h3>
<h3>Calcolo</h3>
<h3>Output</h3>
<h2>Nuove Funzionalità (06/01/2024)</h2>
<h3>Ottimizzazioni Calcolo</h3>
<ol>
<li>Miglioramento precisione interpolazione dati</li>
<li>Nuovi controlli di validazione input</li>
<li>Gestione ottimizzata della cache</li>
<li>Logging dettagliato delle operazioni</li>
<li>Gestione errori avanzata</li>
</ol>
<h3>Validazione Risultati</h3>
<ol>
<li>Controlli automatici di coerenza</li>
<li>Confronto con valori attesi</li>
<li>Verifica limiti normativi</li>
<li>Log dettagliato delle anomalie</li>
<li>Sistema di notifica errori</li>
</ol>
<h3>Integrazione API</h3>
<ol>
<li>Nuovo endpoint </li>
<li>Gestione asincrona dei calcoli</li>
<li>Cache dei risultati frequenti</li>
<li>Validazione input/output</li>
<li>Rate limiting per ottimizzazione</li>
</ol>
<h3>Debug e Testing</h3>
<ol>
<li>Nuovi strumenti di debug in </li>
<li>Log dettagliati in </li>
<li>Suite di test automatizzati</li>
<li>Strumenti di analisi performance</li>
<li>Sistema di reporting errori</li>
</ol>
<h2>Note Importanti</h2>
<h3>Validazione Input</h3>
<ol>
<li>Coordinate geografiche valide</li>
<li>Parametri vita nominale e classe d'uso corretti</li>
<li>Categorie suolo e topografica ammissibili</li>
<li>Dati di pericolosità sismica disponibili</li>
</ol>
<h3>Precisione Calcoli</h3>
<ol>
<li>Interpolazione dati griglia</li>
<li>Arrotondamento risultati</li>
<li>Gestione errori numerici</li>
<li>Validazione output</li>
</ol>
<h3>Riferimenti Normativi</h3>
<ol>
<li>NTC 2018</li>
<li>Circolare 2019</li>
<li>Eurocodice 8</li>
<li>Ordinanze PCM </li>
</ol>
<h2>Ricalcolo Parametri</h2>
<h3>Interfaccia Utente</h3>
<p>L'applicazione permette il ricalcolo dei parametri sismici attraverso:</p>
<ol>
<li>Input vita nominale (default: 50 anni)</li>
<li>Selezione classe edificio (I, II, III, IV)</li>
<li>Selezione categoria terreno (A, B, C, D, E)</li>
<li>Selezione categoria topografica (T1, T2, T3, T4)</li>
</ol>
<h3>Processo di Ricalcolo</h3>
<h3>Visualizzazione Risultati</h3>
<p>I risultati vengono mostrati in una tabella con:</p>
<ul>
<li>Stati Limite (SLO, SLD, SLV, SLC)</li>
<li>Tempo di ritorno TR [anni]</li>
<li>Accelerazione ag [g]</li>
<li>Fattore amplificazione F0</li>
<li>Periodo TC* [s] </li>
</ul>
<h1>Analisi Normative Calcolo Sismico</h1>
<h2>Documenti di Riferimento</h2>
<ol>
<li>Azione sismica 2008.pdf</li>
<li>2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf</li>
<li>allegati.pdf</li>
</ol>
<h2>Obiettivi dell'Analisi</h2>
<ol>
<li>Verificare le formule di calcolo ufficiali</li>
<li>Controllare i metodi di interpolazione</li>
<li>Validare i coefficienti utilizzati</li>
<li>Assicurare la conformità con la normativa</li>
</ol>
<h2>Struttura dell'Analisi</h2>
<p>Per ogni documento analizzeremo:</p>
<ol>
<li>
<p><strong>Formule di Calcolo</strong></p>
<ul>
<li>Equazioni fondamentali</li>
<li>Coefficienti e parametri</li>
<li>Metodi di interpolazione</li>
<li>Limiti e condizioni</li>
</ul>
</li>
<li>
<p><strong>Parametri di Input</strong></p>
<ul>
<li>Coordinate geografiche</li>
<li>Caratteristiche del sito</li>
<li>Parametri strutturali</li>
<li>Coefficienti di amplificazione</li>
</ul>
</li>
<li>
<p><strong>Procedure di Calcolo</strong></p>
<ul>
<li>Sequenza operazioni</li>
<li>Validazioni intermedie</li>
<li>Controlli di coerenza</li>
<li>Arrotondamenti</li>
</ul>
</li>
<li>
<p><strong>Output Attesi</strong></p>
<ul>
<li>Formato risultati</li>
<li>Precisione richiesta</li>
<li>Verifiche di validità</li>
<li>Limiti accettabili</li>
</ul>
</li>
</ol>
<h2>Analisi Documento 1: Azione sismica 2008.pdf</h2>
<h3>1. Metodo di Interpolazione</h3>
<p>Il documento specifica che l'interpolazione dei valori deve essere effettuata secondo la seguente procedura:</p>
<ol>
<li>
<p><strong>Identificazione Punti Griglia</strong>:</p>
<ul>
<li>Individuare i 4 punti della griglia più vicini al punto di interesse</li>
<li>Verificare che il punto sia all'interno del quadrilatero formato dai 4 punti</li>
</ul>
</li>
<li>
<p><strong>Formula di Interpolazione</strong>:</p>
<p>dove:</p>
<ul>
<li>p è il valore nel punto di interesse</li>
<li>p1, p2, p3, p4 sono i valori nei punti della griglia</li>
<li>x, y sono le distanze normalizzate (tra 0 e 1)</li>
</ul>
</li>
<li>
<p><strong>Parametri da Interpolare</strong>:</p>
<ul>
<li>ag (accelerazione orizzontale massima)</li>
<li>F0 (fattore di amplificazione spettrale massima)</li>
<li>TC* (periodo di inizio del tratto a velocità costante)</li>
</ul>
</li>
</ol>
<h3>2. Calcolo Periodo di Ritorno (TR)</h3>
<p>Il periodo di ritorno TR viene calcolato come:</p>
<p>dove:</p>
<ul>
<li>VR = VN * CU (periodo di riferimento)</li>
<li>VN = vita nominale</li>
<li>CU = coefficiente d'uso</li>
<li>PVR = probabilità di superamento nel periodo di riferimento</li>
</ul>
<h3>3. Stati Limite e Probabilità</h3>
<p>Stati Limite di Esercizio (SLE):</p>
<ul>
<li>SLO: PVR = 81%</li>
<li>SLD: PVR = 63%</li>
</ul>
<p>Stati Limite Ultimi (SLU):</p>
<ul>
<li>SLV: PVR = 10%</li>
<li>SLC: PVR = 5%</li>
</ul>
<h2>Analisi Documento 2: 2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf</h2>
<h3>1. Categorie di Sottosuolo</h3>
<p>Il documento definisce 5 categorie di sottosuolo principali:</p>
<p>A. Ammassi rocciosi o terreni molto rigidi<br />
B. Rocce tenere e depositi di terreni a grana grossa molto addensati<br />
C. Depositi di terreni a grana grossa mediamente addensati<br />
D. Depositi di terreni a grana grossa scarsamente addensati<br />
E. Terreni con caratteristiche meccaniche particolarmente scadenti</p>
<h3>2. Coefficienti di Amplificazione Stratigrafica (SS e CC)</h3>
<p>Per ogni categoria di sottosuolo, si applicano i seguenti coefficienti:</p>
<p><strong>Categoria A:</strong></p>
<ul>
<li>SS = 1.00</li>
<li>CC = 1.00</li>
</ul>
<p><strong>Categoria B:</strong></p>
<ul>
<li>SS = 1.00 ≤ 1.40 - 0.40 <em> F0 </em> ag/g ≤ 1.20</li>
<li>CC = 1.10 <em> (TC</em>)-0.20</li>
</ul>
<p><strong>Categoria C:</strong></p>
<ul>
<li>SS = 1.00 ≤ 1.70 - 0.60 <em> F0 </em> ag/g ≤ 1.50</li>
<li>CC = 1.05 <em> (TC</em>)-0.33</li>
</ul>
<p><strong>Categoria D:</strong></p>
<ul>
<li>SS = 0.90 ≤ 2.40 - 1.50 <em> F0 </em> ag/g ≤ 1.80</li>
<li>CC = 1.25 <em> (TC</em>)-0.50</li>
</ul>
<p><strong>Categoria E:</strong></p>
<ul>
<li>SS = 1.00 ≤ 2.00 - 1.10 <em> F0 </em> ag/g ≤ 1.60</li>
<li>CC = 1.15 <em> (TC</em>)-0.40</li>
</ul>
<h3>3. Coefficienti Topografici (ST)</h3>
<p>Categorie topografiche e relativi coefficienti:</p>
<p>T1. ST = 1.0 (superficie pianeggiante)<br />
T2. ST = 1.2 (pendii con inclinazione &gt; 15°)<br />
T3. ST = 1.2 (rilievi con larghezza cresta &lt; altezza)<br />
T4. ST = 1.4 (rilievi con larghezza cresta molto minore dell'altezza)</p>
<h3>4. Calcolo dei Periodi TC e TB</h3>
<p>Il periodo TC è espresso in secondi e viene calcolato come:</p>
<p>dove:</p>
<ul>
<li>TC* è il periodo di riferimento (da interpolazione) espresso in secondi</li>
<li>CC è il coefficiente di categoria del sottosuolo (adimensionale)</li>
</ul>
<p>Il periodo TB, anch'esso in secondi, si calcola come:</p>
<p>IMPORTANTE:</p>
<ol>
<li>
<p>TC* viene ottenuto per interpolazione dai valori della griglia di riferimento</p>
</li>
<li>
<p>CC dipende dalla categoria di sottosuolo secondo le formule:</p>
<ul>
<li>Categoria A: CC = 1.00</li>
<li>Categoria B: CC = 1.10 <em> (TC</em>)^(-0.20)</li>
<li>Categoria C: CC = 1.05 <em> (TC</em>)^(-0.33)</li>
<li>Categoria D: CC = 1.25 <em> (TC</em>)^(-0.50)</li>
<li>Categoria E: CC = 1.15 <em> (TC</em>)^(-0.40)</li>
</ul>
</li>
<li>
<p>I valori di TC e TB devono essere arrotondati a 3 decimali</p>
</li>
<li>
<p>TC e TB non possono essere negativi o nulli</p>
</li>
<li>
<p>TC deve essere sempre maggiore di TB</p>
</li>
</ol>
<p>Esempio di calcolo per categoria B:</p>
<ul>
<li>TC* = 0.306 s (da interpolazione)</li>
<li>CC = 1.10 * (0.306)^(-0.20) = 1.357</li>
<li>TC = 1.357 * 0.306 = 0.415 s</li>
<li>TB = 0.415 / 3 = 0.138 s</li>
</ul>
<h2>Analisi Documento 3: allegati.pdf</h2>
<h3>1. Spettro di Risposta Elastico in Accelerazione</h3>
<p>Lo spettro di risposta elastico in accelerazione è definito dalle seguenti espressioni:</p>
<ol>
<li>
<p><strong>Per 0 ≤ T &lt; TB</strong>:</p>
</li>
<li>
<p><strong>Per TB ≤ T &lt; TC</strong>:</p>
</li>
<li>
<p><strong>Per TC ≤ T &lt; TD</strong>:</p>
</li>
<li>
<p><strong>Per TD ≤ T</strong>:</p>
</li>
</ol>
<p>dove:</p>
<ul>
<li>S = SS * ST (coefficiente che tiene conto della categoria di sottosuolo e delle condizioni topografiche)</li>
<li>η = √(10/(5+ξ)) ≥ 0.55 (fattore che altera lo spettro elastico per coefficienti di smorzamento viscosi ξ diversi dal 5%)</li>
<li>T = periodo di vibrazione</li>
<li>F0 = fattore che quantifica l'amplificazione spettrale massima</li>
<li>TC = CC <em> TC</em> (periodo corrispondente all'inizio del tratto a velocità costante)</li>
<li>TB = TC/3 (periodo corrispondente all'inizio del tratto ad accelerazione costante)</li>
<li>TD = 4.0 * (ag/g) + 1.6 (periodo corrispondente all'inizio del tratto a spostamento costante)</li>
</ul>
<h3>2. Fattore di Struttura (q)</h3>
<p>Il fattore di struttura q da utilizzare per ciascuno stato limite è:</p>
<ul>
<li>
<p><strong>Stati Limite di Esercizio (SLE)</strong>:</p>
<ul>
<li>SLO: q = 1</li>
<li>SLD: q = 1</li>
</ul>
</li>
<li>
<p><strong>Stati Limite Ultimi (SLU)</strong>:</p>
<ul>
<li>SLV: q &gt; 1 (dipende dalla tipologia strutturale)</li>
<li>SLC: q &gt; 1 (dipende dalla tipologia strutturale)</li>
</ul>
</li>
</ul>
<p>Il valore di q dipende da:</p>
<ol>
<li>Materiale strutturale (CA, CAP, Acciaio, Legno, Muratura)</li>
<li>Tipologia strutturale</li>
<li>Regolarità in pianta e in altezza</li>
<li>Classe di duttilità</li>
</ol>
<h3>3. Spettro di Progetto</h3>
<p>Lo spettro di progetto Sd(T) si ottiene dallo spettro elastico sostituendo η con 1/q:</p>
<h2>Stato Analisi</h2>
<ul>
<li>[x] Azione sismica 2008.pdf</li>
<li>[x] 2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf</li>
<li>[x] allegati.pdf</li>
</ul>
<h2>Note e Osservazioni</h2>
<ol>
<li>
<p><strong>Differenze Riscontrate</strong>:</p>
<ul>
<li>Il nostro codice attuale non implementa correttamente la formula di interpolazione</li>
<li>I periodi di ritorno calcolati sembrano corretti</li>
<li>La precisione dei risultati è conforme (3 decimali)</li>
</ul>
</li>
<li>
<p><strong>Azioni Necessarie</strong>:</p>
<ul>
<li>Correggere la formula di interpolazione</li>
<li>Aggiungere validazioni sui limiti geografici</li>
<li>Implementare controlli di coerenza sui risultati</li>
</ul>
</li>
<li>
<p><strong>Nuove Differenze Riscontrate</strong>:</p>
<ul>
<li>I coefficienti di amplificazione stratigrafica devono rispettare limiti precisi</li>
<li>Il calcolo di TC e TB deve considerare il coefficiente CC</li>
<li>I coefficienti topografici devono essere applicati correttamente</li>
</ul>
</li>
<li>
<p><strong>Ulteriori Azioni Necessarie</strong>:</p>
<ul>
<li>Implementare i controlli sui limiti dei coefficienti SS</li>
<li>Aggiungere il calcolo corretto di TC e TB</li>
<li>Verificare l'applicazione dei coefficienti topografici</li>
</ul>
</li>
<li>
<p><strong>Ulteriori Differenze Riscontrate</strong>:</p>
<ul>
<li>Il calcolo dello smorzamento η non è implementato</li>
<li>Il fattore di struttura q non è considerato</li>
<li>Lo spettro di progetto non è calcolato</li>
</ul>
</li>
<li>
<p><strong>Azioni Finali Necessarie</strong>:</p>
<ul>
<li>Implementare il calcolo completo dello spettro elastico</li>
<li>Aggiungere il calcolo del fattore η per diversi smorzamenti</li>
<li>Implementare il calcolo dello spettro di progetto con fattore q</li>
<li>Aggiungere validazioni per tutti i parametri di input</li>
</ul>
</li>
</ol>
<p><em>L'analisi di tutti i documenti è completata. Possiamo procedere con l'implementazione delle correzioni necessarie.</em> </p>
<h2>Sviluppi Futuri e Innovazioni</h2>
<p>Il progetto A.S.D.P. è in continua evoluzione, con focus su:</p>
<ul>
<li>Miglioramento degli algoritmi di IA per una più precisa previsione della risposta sismica</li>
<li>Sviluppo di nuovi materiali magnetoreologici con prestazioni superiori</li>
<li>Implementazione di sistemi di monitoraggio in tempo reale</li>
<li>Integrazione con sistemi di early warning sismico</li>
<li>Ottimizzazione dei costi di produzione e installazione</li>
</ul>

        </div>
    </div>

    <button class="print-button" id="printBtn" title="Stampa documento">
        <i class="fas fa-print"></i>
    </button>

    <script>
    $(document).ready(function() {
        // Configurazione Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                curve: 'linear',
                padding: 20,
                useMaxWidth: true,
                htmlLabels: false,
                nodeSpacing: 80,
                rankSpacing: 80,
                diagramPadding: 20
            },
            layout: 'dagre'
        });

        // Inizializza Mermaid dopo il caricamento della pagina
        setTimeout(function() {
            mermaid.run({
                querySelector: '.mermaid'
            });
        }, 500);
        
        // Rimuovi eventuali pulsanti di chiusura esistenti
        $('.close-button').remove();
        
        // Aggiungi pulsante chiudi dinamicamente solo se non esiste già
        if ($('#closeBtn').length === 0) {
            $('body').append(
                $('<button>')
                    .attr('id', 'closeBtn')
                    .addClass('close-button')
                    .attr('title', 'Chiudi')
                    .html('<i class="fas fa-times"></i>')
                    .on('click', function() {
                        // Verifica se siamo in un iframe
                        if (window !== window.top) {
                            // Accedi direttamente al popup nel parent e chiudilo
                            const frameElement = window.frameElement;
                            if (frameElement) {
                                const popup = frameElement.closest('.doc-popup');
                                if (popup) {
                                    popup.style.display = 'none';
                                }
                            }
                        } else {
                            // Altrimenti chiudi la finestra
                            window.close();
                        }
                    })
            );
        }

        // Gestione link interni
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            const target = $($(this).attr('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 20
                }, 500);
                
                // Aggiorna URL senza ricaricare la pagina
                history.pushState(null, null, $(this).attr('href'));
                
                // Evidenzia la sezione attiva
                $('.doc-section').removeClass('active');
                target.addClass('active');
                
                // Evidenzia il link nell'indice
                $('.toc a').removeClass('active');
                $('.toc a[href="' + $(this).attr('href') + '"]').addClass('active');
            }
        });
        
        // Genera indice laterale
        const toc = $('#toc');
        $('.doc-section').each(function() {
            const section = $(this);
            const id = section.attr('id');
            const title = section.find('h3').first().text();
            
            toc.append(
                $('<li>').append(
                    $('<a>')
                        .attr('href', '#' + id)
                        .text(title)
                )
            );
        });
        
        // Evidenzia sezione corrente durante lo scroll
        $(window).scroll(function() {
            const scrollPos = $(window).scrollTop();
            
            $('.doc-section').each(function() {
                const section = $(this);
                const sectionTop = section.offset().top - 100;
                const sectionBottom = sectionTop + section.outerHeight();
                
                if (scrollPos >= sectionTop && scrollPos < sectionBottom) {
                    $('.doc-section').removeClass('active');
                    section.addClass('active');
                    
                    $('.toc a').removeClass('active');
                    $('.toc a[href="#' + section.attr('id') + '"]').addClass('active');
                    
                    // Aggiorna URL senza ricaricare la pagina
                    history.replaceState(null, null, '#' + section.attr('id'));
                }
            });
        });
        
        // Gestione pulsante stampa
        $('#printBtn').on('click', function() {
            window.print();
        });
    });
    </script>
</body>
</html>