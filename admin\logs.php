<?php
require_once '../includes/db_config.php';
require_once '../includes/logger.php';
session_start();

// Verifica autenticazione
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit;
}

$logger = Logger::getInstance();

// Parametri di paginazione e filtri
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
$offset = ($page - 1) * $limit;
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// Funzione per ottenere i log dal database con filtri
function getDatabaseLogs($offset = 0, $limit = 50, $filter = '', $dateFrom = '', $dateTo = '') {
    try {
        $conn = getConnection();
        $params = [];
        $whereConditions = [];
        
        $query = "SELECT al.*, u.username 
                 FROM access_logs al 
                 LEFT JOIN users u ON al.user_id = u.id";
        
        // Aggiungi filtri
        if (!empty($filter)) {
            $whereConditions[] = "(u.username LIKE ? OR al.action LIKE ? OR al.ip_address LIKE ?)";
            $filterParam = "%$filter%";
            $params = array_merge($params, [$filterParam, $filterParam, $filterParam]);
        }
        
        // Filtro date
        if (!empty($dateFrom)) {
            $whereConditions[] = "DATE(al.timestamp) >= ?";
            $params[] = $dateFrom;
        }
        if (!empty($dateTo)) {
            $whereConditions[] = "DATE(al.timestamp) <= ?";
            $params[] = $dateTo;
        }
        
        // Aggiungi WHERE se ci sono condizioni
        if (!empty($whereConditions)) {
            $query .= " WHERE " . implode(" AND ", $whereConditions);
        }
        
        $query .= " ORDER BY al.timestamp DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Errore nel recupero dei log: " . $e->getMessage());
        return false;
    }
}

// Funzione per contare il totale dei log
function getTotalLogs($filter = '', $dateFrom = '', $dateTo = '') {
    try {
        $conn = getConnection();
        $params = [];
        $whereConditions = [];
        
        $query = "SELECT COUNT(*) as total FROM access_logs al LEFT JOIN users u ON al.user_id = u.id";
        
        if (!empty($filter)) {
            $whereConditions[] = "(u.username LIKE ? OR al.action LIKE ? OR al.ip_address LIKE ?)";
            $filterParam = "%$filter%";
            $params = array_merge($params, [$filterParam, $filterParam, $filterParam]);
        }
        
        if (!empty($dateFrom)) {
            $whereConditions[] = "DATE(al.timestamp) >= ?";
            $params[] = $dateFrom;
        }
        if (!empty($dateTo)) {
            $whereConditions[] = "DATE(al.timestamp) <= ?";
            $params[] = $dateTo;
        }
        
        if (!empty($whereConditions)) {
            $query .= " WHERE " . implode(" AND ", $whereConditions);
        }
        
        $stmt = $conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    } catch (PDOException $e) {
        error_log("Errore nel conteggio dei log: " . $e->getMessage());
        return 0;
    }
}

// Funzione ottimizzata per leggere i log dal file con parsing migliorato
function getFileLogs($lines = 50) {
    $logger = Logger::getInstance();
    $logFile = $logger->getLogPath();

    if (!file_exists($logFile)) {
        return [];
    }

    $logs = [];
    $file = new SplFileObject($logFile, 'r');
    $file->seek(PHP_INT_MAX);
    $totalLines = $file->key();

    $startLine = max(0, $totalLines - ($lines * 4)); // Leggi più righe per catturare log completi
    $currentLog = '';

    $file->seek($startLine);
    while (!$file->eof()) {
        $line = $file->fgets();

        // Identifica l'inizio di un nuovo log entry
        if (preg_match('/^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]/', $line)) {
            if (!empty($currentLog)) {
                $logs[] = parseLogEntry($currentLog);
            }
            $currentLog = $line;
        } else {
            $currentLog .= $line;
        }
    }

    if (!empty($currentLog)) {
        $logs[] = parseLogEntry($currentLog);
    }

    // Prendi solo gli ultimi N log entries
    $logs = array_slice(array_reverse($logs), 0, $lines);

    return $logs;
}

// Funzione per parsare e formattare un singolo log entry
function parseLogEntry($logText) {
    // Pattern per estrarre le componenti del log
    $pattern = '/^\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] \[([A-Z]+)\] \[Session: ([^\]]*)\] \[User: ([^\]]*)\] \[Role: ([^\]]*)\] \[([^\]]*)\] (.*)$/s';

    if (preg_match($pattern, $logText, $matches)) {
        return [
            'timestamp' => $matches[1],
            'level' => $matches[2],
            'session' => $matches[3],
            'user' => $matches[4],
            'role' => $matches[5],
            'ip' => $matches[6],
            'message' => trim($matches[7]),
            'raw' => $logText
        ];
    }

    // Fallback per log non strutturati
    return [
        'timestamp' => 'N/A',
        'level' => 'INFO',
        'session' => 'N/A',
        'user' => 'N/A',
        'role' => 'N/A',
        'ip' => 'N/A',
        'message' => trim($logText),
        'raw' => $logText
    ];
}

// Recupera i dati
$totalLogs = getTotalLogs($filter, $dateFrom, $dateTo);
$totalPages = ceil($totalLogs / $limit);
$dbLogs = getDatabaseLogs($offset, $limit, $filter, $dateFrom, $dateTo);
$fileLogs = getFileLogs($limit);

// Includi l'header admin
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="header-content">
                <h1>Log di Sistema</h1>
                <p class="subtitle">Visualizza e gestisci i log di sistema e accesso</p>
            </div>
            <div class="header-actions">
                <button class="log-btn" onclick="downloadLogs()">
                    <i class="fas fa-download"></i> Scarica Log
                </button>
                <button class="log-btn danger" onclick="clearLogs()">
                    <i class="fas fa-trash"></i> Pulisci Log
                </button>
            </div>
        </div>

        <div class="logs-section">
            <!-- Filtri -->
            <div class="logs-card filter-card">
                <div class="filter-content">
                    <form id="filterForm" class="filter-form">
                        <div class="filter-row">
                            <div class="filter-group search">
                                <i class="fas fa-search"></i>
                                <input type="text" name="filter" placeholder="Cerca per utente, azione o IP..." 
                                       value="<?php echo htmlspecialchars($filter); ?>">
                            </div>
                            <div class="filter-group date-range">
                                <div class="date-input">
                                    <i class="fas fa-calendar-alt"></i>
                                    <input type="date" name="date_from" value="<?php echo htmlspecialchars($dateFrom); ?>" 
                                           placeholder="Data inizio">
                                </div>
                                <div class="date-input">
                                    <i class="fas fa-calendar-alt"></i>
                                    <input type="date" name="date_to" value="<?php echo htmlspecialchars($dateTo); ?>" 
                                           placeholder="Data fine">
                                </div>
                            </div>
                        </div>
                        <div class="filter-row actions">
                            <div class="filter-group">
                                <select name="limit" class="select-rows">
                                    <option value="25" <?php echo $limit == 25 ? 'selected' : ''; ?>>25 righe</option>
                                    <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>50 righe</option>
                                    <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>100 righe</option>
                                </select>
                            </div>
                            <button type="submit" class="log-btn primary">
                                <i class="fas fa-filter"></i> Applica Filtri
                            </button>
                            <button type="reset" class="log-btn secondary" onclick="resetFilters()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Log Container -->
            <div class="logs-container">
                <!-- Card Log Database -->
                <div class="logs-card db-logs">
                    <div class="card-header">
                        <h2><i class="fas fa-database"></i> Log Database</h2>
                        <span class="log-count"><?php echo number_format($totalLogs); ?> record</span>
                    </div>
                    <div class="logs-content">
                        <?php if ($dbLogs): ?>
                            <div class="table-container">
                                <table class="logs-table">
                                    <thead>
                                        <tr>
                                            <th>Data/Ora</th>
                                            <th>Utente</th>
                                            <th>Azione</th>
                                            <th>IP</th>
                                            <th>User Agent</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($dbLogs as $log): ?>
                                            <tr>
                                                <td><?php echo date('d/m/Y H:i:s', strtotime($log['timestamp'])); ?></td>
                                                <td><?php echo htmlspecialchars($log['username'] ?? 'N/A'); ?></td>
                                                <td><?php echo htmlspecialchars($log['action']); ?></td>
                                                <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                                                <td class="user-agent">
                                                    <span class="tooltip" data-tooltip="<?php echo htmlspecialchars($log['user_agent']); ?>">
                                                        <?php echo htmlspecialchars(substr($log['user_agent'], 0, 50)) . (strlen($log['user_agent']) > 50 ? '...' : ''); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Paginazione -->
                            <?php if ($totalPages > 1): ?>
                            <div class="pagination">
                                <?php if ($page > 1): ?>
                                    <a href="?page=1&limit=<?php echo $limit; ?>&filter=<?php echo urlencode($filter); ?>&date_from=<?php echo urlencode($dateFrom); ?>&date_to=<?php echo urlencode($dateTo); ?>" 
                                       class="page-link" title="Prima pagina">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                    <a href="?page=<?php echo ($page - 1); ?>&limit=<?php echo $limit; ?>&filter=<?php echo urlencode($filter); ?>&date_from=<?php echo urlencode($dateFrom); ?>&date_to=<?php echo urlencode($dateTo); ?>" 
                                       class="page-link" title="Pagina precedente">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                <?php endif; ?>

                                <?php
                                $start = max(1, min($page - 2, $totalPages - 4));
                                $end = min($totalPages, max(5, $page + 2));
                                
                                if ($start > 1): ?>
                                    <span class="page-ellipsis">...</span>
                                <?php endif;

                                for ($i = $start; $i <= $end; $i++): ?>
                                    <a href="?page=<?php echo $i; ?>&limit=<?php echo $limit; ?>&filter=<?php echo urlencode($filter); ?>&date_from=<?php echo urlencode($dateFrom); ?>&date_to=<?php echo urlencode($dateTo); ?>" 
                                       class="page-link <?php echo $page == $i ? 'active' : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endfor;

                                if ($end < $totalPages): ?>
                                    <span class="page-ellipsis">...</span>
                                <?php endif; ?>

                                <?php if ($page < $totalPages): ?>
                                    <a href="?page=<?php echo ($page + 1); ?>&limit=<?php echo $limit; ?>&filter=<?php echo urlencode($filter); ?>&date_from=<?php echo urlencode($dateFrom); ?>&date_to=<?php echo urlencode($dateTo); ?>" 
                                       class="page-link" title="Pagina successiva">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                    <a href="?page=<?php echo $totalPages; ?>&limit=<?php echo $limit; ?>&filter=<?php echo urlencode($filter); ?>&date_from=<?php echo urlencode($dateFrom); ?>&date_to=<?php echo urlencode($dateTo); ?>" 
                                       class="page-link" title="Ultima pagina">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="no-data">
                                <i class="fas fa-info-circle"></i>
                                <p>Nessun log nel database</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Card Log File -->
                <div class="logs-card file-logs">
                    <div class="card-header">
                        <h2><i class="fas fa-file-alt"></i> Log File</h2>
                        <div class="file-controls">
                            <select id="logTypeFilter" onchange="filterLogType(this.value)">
                                <option value="all">Tutti i tipi</option>
                                <option value="info">Info</option>
                                <option value="warning">Warning</option>
                                <option value="error">Error</option>
                            </select>
                        </div>
                    </div>
                    <div class="logs-content">
                        <?php if ($fileLogs): ?>
                            <div class="file-logs-container">
                                <?php foreach ($fileLogs as $log): ?>
                                    <?php
                                    $logClass = 'log-info';
                                    $level = is_array($log) ? $log['level'] : 'INFO';

                                    if ($level === 'WARNING') {
                                        $logClass = 'log-warning';
                                    } elseif ($level === 'ERROR' || $level === 'CRITICAL') {
                                        $logClass = 'log-error';
                                    } elseif ($level === 'DEBUG') {
                                        $logClass = 'log-debug';
                                    }
                                    ?>
                                    <div class="log-entry <?php echo $logClass; ?>" data-level="<?php echo strtolower($level); ?>">
                                        <?php if (is_array($log)): ?>
                                            <div class="log-header">
                                                <span class="log-timestamp"><?php echo htmlspecialchars($log['timestamp']); ?></span>
                                                <span class="log-level <?php echo strtolower($level); ?>"><?php echo htmlspecialchars($level); ?></span>
                                                <span class="log-user">User: <?php echo htmlspecialchars($log['user']); ?></span>
                                                <?php if ($log['ip'] !== 'N/A'): ?>
                                                    <span class="log-ip">IP: <?php echo htmlspecialchars($log['ip']); ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="log-message">
                                                <?php
                                                $message = $log['message'];
                                                // Se il messaggio è molto lungo, mostra solo le prime righe
                                                $lines = explode("\n", $message);
                                                if (count($lines) > 5) {
                                                    $shortMessage = implode("\n", array_slice($lines, 0, 3));
                                                    echo '<div class="log-preview">' . htmlspecialchars($shortMessage) . '</div>';
                                                    echo '<div class="log-full" style="display: none;">' . htmlspecialchars($message) . '</div>';
                                                    echo '<button class="toggle-log-btn" onclick="toggleLogDetails(this)">Mostra tutto</button>';
                                                } else {
                                                    echo htmlspecialchars($message);
                                                }
                                                ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="log-raw">
                                                <pre><?php echo htmlspecialchars($log); ?></pre>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="no-data">
                                <i class="fas fa-info-circle"></i>
                                <p>Nessun log nel file</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include 'admin_footer.php'; ?>
</div>

<style>
.admin-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #121212;
}

.content {
    flex: 1;
    max-width: 1400px;
    margin: 1rem auto;
    padding: 0 1rem;
    width: 100%;
}

/* Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 0.5rem;
    background: #1E1E1E;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-content h1 {
    color: #FF7043;
    font-size: 1.5rem;
    margin: 0;
    font-weight: 500;
}

.subtitle {
    color: #BDBDBD;
    margin: 0.25rem 0 0 0;
    font-size: 0.9rem;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

/* Filtri */
.filter-card {
    margin-bottom: 1rem;
    background: #1E1E1E;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.filter-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.filter-row {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group.search {
    flex: 2;
    min-width: 300px;
    position: relative;
}

.filter-group.search i {
    position: absolute;
    left: 0.75rem;
    color: #666;
}

.filter-group.search input {
    width: 100%;
    padding-left: 2.5rem;
}

.filter-group.date-range {
    flex: 1;
    min-width: 300px;
    display: flex;
    gap: 0.5rem;
}

.date-input {
    flex: 1;
    position: relative;
}

.date-input i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.date-input input {
    width: 100%;
    padding-left: 2.5rem;
}

.filter-row.actions {
    justify-content: flex-end;
}

.select-rows {
    min-width: 120px;
}

/* Input Styles */
input, select {
    padding: 0.75rem;
    border: 1px solid #333;
    border-radius: 4px;
    background: #262626;
    color: #FFFFFF;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

input:focus, select:focus {
    border-color: #FF7043;
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 112, 67, 0.2);
}

/* Buttons */
.log-btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    min-width: 120px;
    justify-content: center;
}

.log-btn.primary {
    background: #FF7043;
    color: #FFFFFF;
}

.log-btn.secondary {
    background: #424242;
    color: #FFFFFF;
}

.log-btn.danger {
    background: #F44336;
    color: #FFFFFF;
}

.log-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.log-btn.primary:hover {
    background: #F4511E;
}

.log-btn.secondary:hover {
    background: #616161;
}

.log-btn.danger:hover {
    background: #D32F2F;
}

/* Logs Container */
.logs-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.logs-card {
    background: #1E1E1E;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
}

.card-header {
    padding: 1rem;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    color: #FF7043;
    font-size: 1.1rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.log-count {
    color: #BDBDBD;
    font-size: 0.9rem;
}

.logs-content {
    flex: 1;
    overflow: auto;
    padding: 1rem;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.logs-table th {
    background: #262626;
    color: #FF7043;
    font-weight: 500;
    text-align: left;
    padding: 0.75rem;
    position: sticky;
    top: 0;
    z-index: 1;
}

.logs-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #333;
    color: #BDBDBD;
}

.logs-table tr:hover td {
    background: #262626;
}

/* File Logs */
.file-logs-container {
    font-family: 'Consolas', monospace;
    font-size: 0.85rem;
}

.log-entry {
    padding: 0.5rem;
    border-bottom: 1px solid #333;
    transition: background-color 0.2s;
}

.log-entry:hover {
    background: #262626;
}

.log-info {
    border-left: 3px solid #2196F3;
}

.log-warning {
    border-left: 3px solid #FFC107;
    background: rgba(255, 193, 7, 0.1);
}

.log-error {
    border-left: 3px solid #F44336;
    background: rgba(244, 67, 54, 0.1);
}

/* Tooltip */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.5rem;
    background: #333;
    color: #FFF;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: normal;
    max-width: 300px;
    z-index: 1000;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
    margin-top: 1rem;
    padding: 1rem;
    background: #262626;
    border-radius: 4px;
}

.page-link {
    padding: 0.5rem 0.75rem;
    color: #BDBDBD;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
    min-width: 32px;
    text-align: center;
}

.page-link:hover {
    background: #333;
    color: #FFF;
}

.page-link.active {
    background: #FF7043;
    color: #FFF;
}

.page-ellipsis {
    color: #666;
    padding: 0.5rem;
}

/* No Data State */
.no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #666;
    text-align: center;
}

.no-data i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .logs-container {
        grid-template-columns: 1fr;
    }
    
    .filter-row {
        flex-direction: column;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-group.search,
    .filter-group.date-range {
        min-width: 100%;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-actions {
        width: 100%;
        justify-content: stretch;
    }
    
    .log-btn {
        flex: 1;
    }
    
    .pagination {
        flex-wrap: wrap;
    }
}

/* Dark Mode Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #FF7043;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #F4511E;
}
</style>

<script>
// Funzione per filtrare i tipi di log
function filterLogType(type) {
    const logEntries = document.querySelectorAll('.log-entry');
    logEntries.forEach(entry => {
        if (type === 'all') {
            entry.style.display = '';
        } else {
            entry.style.display = entry.classList.contains(`log-${type}`) ? '' : 'none';
        }
    });
}

// Funzione per scaricare i log
function downloadLogs() {
    if (confirm('Vuoi scaricare i log?')) {
        window.location.href = 'download_logs.php';
    }
}

// Funzione per pulire i log
function clearLogs() {
    if (confirm('Sei sicuro di voler eliminare tutti i log? Questa azione non può essere annullata.')) {
        fetch('clear_logs.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Log eliminati con successo');
                    window.location.reload();
                } else {
                    alert('Errore durante l\'eliminazione dei log: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Errore:', error);
                alert('Si è verificato un errore durante l\'eliminazione dei log');
            });
    }
}

// Funzione per resettare i filtri
function resetFilters() {
    const form = document.getElementById('filterForm');
    if (form) {
        form.reset();
        form.submit();
    }
}

// Gestione tooltip
function initTooltips() {
    const tooltips = document.querySelectorAll('.tooltip');
    if (!tooltips || tooltips.length === 0) return;

    tooltips.forEach(tooltip => {
        // Rimuovi eventuali listener esistenti
        tooltip.removeEventListener('mouseenter', showTooltip);
        tooltip.removeEventListener('mouseleave', hideTooltip);
        
        // Aggiungi i nuovi listener
        tooltip.addEventListener('mouseenter', showTooltip);
        tooltip.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip() {
    const text = this.getAttribute('data-tooltip');
    if (!text) return;
    
    // Rimuovi eventuali tooltip esistenti
    const existingTooltip = this.querySelector('.tooltip-text');
    if (existingTooltip) existingTooltip.remove();
    
    // Crea il nuovo tooltip
    const tooltipEl = document.createElement('div');
    tooltipEl.className = 'tooltip-text';
    tooltipEl.textContent = text;
    this.appendChild(tooltipEl);
}

function hideTooltip() {
    const tooltipEl = this.querySelector('.tooltip-text');
    if (tooltipEl) tooltipEl.remove();
}

// Inizializza i tooltip quando il DOM è caricato
document.addEventListener('DOMContentLoaded', initTooltips);

// Reinizializza i tooltip dopo il caricamento di nuovi dati (se necessario)
if (document.readyState === 'complete') {
    initTooltips();
}
</script>
